#!/usr/bin/env node

// Test the API endpoint to verify database connection
async function testAPI() {
  console.log('🔍 Testing API endpoint...');
  
  try {
    const response = await fetch('http://localhost:3000/api/test-db');
    
    if (!response.ok) {
      console.log(`❌ API returned status: ${response.status}`);
      const text = await response.text();
      console.log('Response:', text);
      return;
    }
    
    const data = await response.json();
    console.log('✅ API Response:', JSON.stringify(data, null, 2));
    
  } catch (error) {
    console.error('❌ API test failed:', error.message);
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('💡 Development server might not be running on port 3000');
      console.log('   Try running: bun run dev');
    }
  }
}

// Wait a moment for server to start, then test
setTimeout(testAPI, 5000);
