/**
 * User Service for BuddyChip
 *
 * Handles user operations and ensures users exist in database
 */

import { clerkClient } from "@clerk/nextjs/server";
import type {
  PlanFeature,
  SubscriptionPlan,
  User,
} from "../../prisma/generated";
import { prisma, safeDbOperation, testDatabaseConnection } from "./db-utils";

export type UserWithPlan = User & {
  plan: SubscriptionPlan & {
    features: PlanFeature[];
  };
};

/**
 * Get or create user in database (just-in-time creation)
 * Used as fallback when webhooks haven't synced yet
 */
export async function getOrCreateUser(
  clerkUserId: string,
  userData?: {
    email?: string;
    name?: string;
    avatar?: string;
  }
): Promise<UserWithPlan> {
  console.log(`🔍 UserService (Web): Getting or creating user: ${clerkUserId}`);

  // Test database connection first with retry logic
  const isConnected = await testDatabaseConnection(2); // 2 retries for faster response
  if (!isConnected) {
    console.error("❌ UserService: Database connection failed after retries");
    throw new Error(
      "Database is temporarily unavailable. Please try again in a few moments. This may be due to a paused Supabase instance that needs to wake up."
    );
  }

  // Try to find existing user with safe operation
  let user = await safeDbOperation(
    () =>
      prisma.user.findUnique({
        where: { id: clerkUserId },
        include: {
          plan: {
            include: {
              features: true,
            },
          },
        },
      }),
    `find user ${clerkUserId}`
  );

  // If user doesn't exist, create them with default plan (free tier)
  if (!user) {
    // If no userData provided, try to fetch from Clerk
    let finalUserData = userData;
    if (!userData?.email && !userData?.name && !userData?.avatar) {
      console.log("🔍 UserService: No user data provided, fetching from Clerk...");
      try {
        const clerk = await clerkClient();
        const clerkUser = await clerk.users.getUser(clerkUserId);

        // Get primary email
        const primaryEmail = clerkUser.emailAddresses.find(
          email => email.id === clerkUser.primaryEmailAddressId
        ) || clerkUser.emailAddresses[0];

        finalUserData = {
          email: primaryEmail?.emailAddress,
          name: clerkUser.firstName && clerkUser.lastName
            ? `${clerkUser.firstName} ${clerkUser.lastName}`
            : clerkUser.firstName || undefined,
          avatar: clerkUser.imageUrl,
        };

        console.log("✅ UserService: Fetched user data from Clerk:", {
          email: finalUserData.email,
          name: finalUserData.name,
          hasAvatar: !!finalUserData.avatar,
        });
      } catch (clerkError) {
        console.warn("⚠️ UserService: Failed to fetch user from Clerk:", clerkError);
        finalUserData = userData || {};
      }
    }

    let defaultPlan = await prisma.subscriptionPlan.findFirst({
      where: { name: "free" },
      include: {
        features: true,
      },
    });

    // If default plan doesn't exist, create it
    if (!defaultPlan) {
      console.log("⚠️ UserService: Default free plan not found, creating it...");

      // Create the default free plan
      defaultPlan = await prisma.subscriptionPlan.create({
        data: {
          name: "free",
          displayName: "Free",
          description: "Get started with basic AI features",
          price: 0.0,
          baseUsers: 1,
          isActive: true,
        },
        include: {
          features: true,
        },
      });

      // Create default features for the free plan
      await prisma.planFeature.createMany({
        data: [
          {
            planId: defaultPlan.id,
            feature: "AI_CALLS",
            limit: 50,
          },
          {
            planId: defaultPlan.id,
            feature: "IMAGE_GENERATIONS",
            limit: 5,
          },
          {
            planId: defaultPlan.id,
            feature: "MONITORED_ACCOUNTS",
            limit: 1,
          },
          {
            planId: defaultPlan.id,
            feature: "MENTIONS_PER_MONTH",
            limit: 100,
          },
          {
            planId: defaultPlan.id,
            feature: "MENTIONS_PER_SYNC",
            limit: 25,
          },
          {
            planId: defaultPlan.id,
            feature: "MAX_TOTAL_MENTIONS",
            limit: 50,
          },
          {
            planId: defaultPlan.id,
            feature: "STORAGE_GB",
            limit: 1,
          },
          {
            planId: defaultPlan.id,
            feature: "TEAM_MEMBERS",
            limit: 1,
          },
          {
            planId: defaultPlan.id,
            feature: "COOKIE_API_CALLS",
            limit: 10,
          },
        ],
      });

      // Refetch the plan with features
      defaultPlan = await prisma.subscriptionPlan.findFirst({
        where: { name: "free" },
        include: {
          features: true,
        },
      });

      console.log("✅ UserService: Created default plan with features");
    }

    if (!defaultPlan) {
      throw new Error("Failed to create or find default subscription plan");
    }

    user = await prisma.user.create({
      data: {
        id: clerkUserId,
        email: finalUserData?.email || null,
        name: finalUserData?.name || null,
        avatar: finalUserData?.avatar || null,
        planId: defaultPlan.id,
        lastActiveAt: new Date(),
      },
      include: {
        plan: {
          include: {
            features: true,
          },
        },
      },
    });

    console.log(`✅ UserService: Just-in-time user creation: ${clerkUserId}`);
    console.log("📋 UserService: Created user with plan:", {
      planId: user.planId,
      planName: user.plan.name,
      email: user.email,
      name: user.name,
      features: user.plan.features.map((f) => ({
        feature: f.feature,
        limit: f.limit,
      })),
    });
  } else {
    // Update last active time
    await prisma.user.update({
      where: { id: clerkUserId },
      data: { lastActiveAt: new Date() },
    });
  }

  return user;
}

/**
 * Get user by Clerk ID
 */
export async function getUserById(
  clerkUserId: string
): Promise<UserWithPlan | null> {
  return prisma.user.findUnique({
    where: { id: clerkUserId },
    include: {
      plan: {
        include: {
          features: true,
        },
      },
    },
  });
}

/**
 * Update user information
 */
export async function updateUser(
  clerkUserId: string,
  data: {
    email?: string;
    name?: string;
    avatar?: string;
  }
) {
  return prisma.user.update({
    where: { id: clerkUserId },
    data: {
      ...data,
      lastActiveAt: new Date(),
    },
  });
}

/**
 * Get user's current usage for a feature
 */
export async function getUserUsage(
  clerkUserId: string,
  feature: string,
  billingPeriod?: string
) {
  const currentPeriod = billingPeriod || getCurrentBillingPeriod();

  const usage = await prisma.usageLog.aggregate({
    where: {
      userId: clerkUserId,
      feature: feature as any,
      billingPeriod: currentPeriod,
    },
    _sum: {
      amount: true,
    },
  });

  return usage._sum.amount || 0;
}

/**
 * Check if user can use a feature (rate limiting)
 */
export async function canUserUseFeature(
  clerkUserId: string,
  feature: string
): Promise<{
  allowed: boolean;
  currentUsage: number;
  limit: number;
  resetDate?: Date;
}> {
  console.log(
    "🔍 UserService: Checking feature usage for user:",
    clerkUserId,
    "feature:",
    feature
  );

  // Special handling for monitored accounts - count actual records instead of usage logs
  if (feature === "MONITORED_ACCOUNTS") {
    return await canUserAddMonitoredAccount(clerkUserId);
  }

  const user = await getUserById(clerkUserId);
  if (!user) {
    console.error("❌ UserService: User not found:", clerkUserId);
    console.log(
      "🔄 UserService: This should not happen with JIT user creation, but handling gracefully"
    );
    // Return a restrictive default instead of throwing
    return {
      allowed: false,
      currentUsage: 0,
      limit: 0,
      resetDate: getNextBillingPeriodStart(),
    };
  }
  console.log("👤 UserService: User found:", {
    id: user.id,
    planId: user.planId,
  });

  // Find feature limit for user's plan
  const planFeature = user.plan.features.find(
    (f: PlanFeature) => f.feature === feature
  );
  console.log(
    "📋 UserService: Available plan features:",
    user.plan.features.map((f) => ({ feature: f.feature, limit: f.limit }))
  );
  console.log("🎯 UserService: Target plan feature:", planFeature);

  if (!planFeature) {
    console.log("❌ UserService: Feature not found in plan:", feature);
    return { allowed: false, currentUsage: 0, limit: 0 };
  }

  const limit = planFeature.limit;
  console.log("📊 UserService: Feature limit:", limit);

  // -1 means unlimited
  if (limit === -1) {
    console.log("♾️ UserService: Unlimited feature access");
    return { allowed: true, currentUsage: 0, limit: -1 };
  }

  const currentUsage = await getUserUsage(clerkUserId, feature);
  const allowed = currentUsage < limit;
  console.log("📈 UserService: Usage check:", { currentUsage, limit, allowed });

  const result = {
    allowed,
    currentUsage,
    limit,
    resetDate: getNextBillingPeriodStart(),
  };
  console.log("✅ UserService: Feature check result:", result);
  return result;
}

/**
 * Check if user can add more monitored accounts (special handling)
 */
export async function canUserAddMonitoredAccount(
  clerkUserId: string
): Promise<{
  allowed: boolean;
  currentUsage: number;
  limit: number;
  resetDate?: Date;
}> {
  console.log(
    "🔍 UserService: Checking monitored accounts limit for user:",
    clerkUserId
  );

  const user = await getUserById(clerkUserId);
  if (!user) {
    console.error("❌ UserService: User not found:", clerkUserId);
    return {
      allowed: false,
      currentUsage: 0,
      limit: 0,
      resetDate: getNextBillingPeriodStart(),
    };
  }

  // Find monitored accounts feature limit
  const planFeature = user.plan.features.find(
    (f: PlanFeature) => f.feature === "MONITORED_ACCOUNTS"
  );

  if (!planFeature) {
    console.log("❌ UserService: MONITORED_ACCOUNTS feature not found in plan");
    return { allowed: false, currentUsage: 0, limit: 0 };
  }

  const limit = planFeature.limit;
  console.log("📊 UserService: Monitored accounts limit:", limit);

  // -1 means unlimited
  if (limit === -1) {
    console.log("♾️ UserService: Unlimited monitored accounts");
    return { allowed: true, currentUsage: 0, limit: -1 };
  }

  // Count actual monitored accounts (active ones)
  const currentCount = await prisma.monitoredAccount.count({
    where: {
      userId: clerkUserId,
      isActive: true,
    },
  });

  const allowed = currentCount < limit;
  console.log("📈 UserService: Monitored accounts check:", {
    currentCount,
    limit,
    allowed,
  });

  const result = {
    allowed,
    currentUsage: currentCount,
    limit,
    resetDate: getNextBillingPeriodStart(),
  };
  console.log("✅ UserService: Monitored accounts check result:", result);
  return result;
}

/**
 * Log feature usage
 */
export async function logUsage(
  clerkUserId: string,
  feature: string,
  amount = 1,
  metadata?: any
) {
  return prisma.usageLog.create({
    data: {
      userId: clerkUserId,
      feature: feature as any,
      amount,
      metadata,
      billingPeriod: getCurrentBillingPeriod(),
    },
  });
}

/**
 * Get current billing period (YYYY-MM format)
 */
function getCurrentBillingPeriod(): string {
  const now = new Date();
  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, "0")}`;
}

/**
 * Get start of next billing period
 */
function getNextBillingPeriodStart(): Date {
  const now = new Date();
  return new Date(now.getFullYear(), now.getMonth() + 1, 1);
}

/**
 * Get user dashboard statistics
 */
export async function getUserStats(clerkUserId: string) {
  const [
    monitoredAccountsCount,
    mentionsCount,
    aiResponsesCount,
    currentUsage,
  ] = await Promise.all([
    // Monitored accounts count
    prisma.monitoredAccount.count({
      where: { userId: clerkUserId, isActive: true },
    }),

    // Recent mentions count (this week)
    prisma.mention.count({
      where: {
        OR: [{ userId: clerkUserId }, { account: { userId: clerkUserId } }],
        createdAt: {
          gte: getStartOfWeek(),
        },
      },
    }),

    // AI responses count (total)
    prisma.aIResponse.count({
      where: { userId: clerkUserId },
    }),

    // Current month AI usage
    getUserUsage(clerkUserId, "AI_CALLS"),
  ]);

  return {
    monitoredAccounts: monitoredAccountsCount,
    recentMentions: mentionsCount,
    aiResponses: aiResponsesCount,
    aiCallsThisMonth: currentUsage,
  };
}

/**
 * Get start of current week
 */
function getStartOfWeek(): Date {
  const now = new Date();
  const day = now.getDay();
  const diff = now.getDate() - day;
  return new Date(now.setDate(diff));
}

/**
 * Check if user can use a feature based on Clerk billing
 * This function integrates with Clerk's billing system
 */
export async function canUserUseClerkFeature(
  clerkUserId: string,
  feature: string
): Promise<{
  allowed: boolean;
  currentUsage: number;
  limit: number;
  resetDate?: Date;
}> {
  console.log(
    `🔍 UserService: Checking Clerk billing feature for user: ${clerkUserId}, feature: ${feature}`
  );

  try {
    // Get user with Clerk billing info
    const user = await prisma.user.findUnique({
      where: { id: clerkUserId },
      select: {
        clerkPlanName: true,
        subscriptionStatus: true,
        plan: {
          include: {
            features: true,
          },
        },
      },
    });

    if (!user) {
      console.log("❌ UserService: User not found");
      return { allowed: false, currentUsage: 0, limit: 0 };
    }

    // If user has active Clerk subscription, use Clerk billing logic
    if (user.clerkPlanName && user.subscriptionStatus === "active") {
      console.log(
        `✅ UserService: User has active Clerk plan: ${user.clerkPlanName}`
      );

      // Map Clerk plan names to feature limits
      const clerkPlanLimits = getClerkPlanLimits(user.clerkPlanName, feature);

      if (clerkPlanLimits.limit === -1) {
        console.log(
          "♾️ UserService: Unlimited feature access via Clerk billing"
        );
        return { allowed: true, currentUsage: 0, limit: -1 };
      }

      // Check current usage
      const currentUsage = await getUserUsage(clerkUserId, feature as any);
      const allowed = currentUsage < clerkPlanLimits.limit;

      console.log(`📈 UserService: Clerk billing usage check:`, {
        currentUsage,
        limit: clerkPlanLimits.limit,
        allowed,
      });

      return {
        allowed,
        currentUsage,
        limit: clerkPlanLimits.limit,
        resetDate: getNextBillingPeriodStart(),
      };
    }

    // Fallback to legacy plan system
    console.log("🔄 UserService: Falling back to legacy plan system");
    return await canUserUseFeature(clerkUserId, feature as any);
  } catch (error) {
    console.error(
      "❌ UserService: Error checking Clerk billing feature:",
      error
    );
    // Fallback to legacy system on error
    return await canUserUseFeature(clerkUserId, feature as any);
  }
}

/**
 * Get feature limits for Clerk billing plans
 */
function getClerkPlanLimits(
  planName: string,
  feature: string
): { limit: number } {
  const planLimits: Record<string, Record<string, number>> = {
    free: {
      AI_CALLS: 50,
      IMAGE_GENERATIONS: 5,
      MONITORED_ACCOUNTS: 1,
      MENTIONS_PER_MONTH: 100,
      MENTIONS_PER_SYNC: 25,
      MAX_TOTAL_MENTIONS: 50,
      STORAGE_GB: 1,
      TEAM_MEMBERS: 1,
      COOKIE_API_CALLS: 10,
    },
    "reply-guy": {
      AI_CALLS: 1000,
      IMAGE_GENERATIONS: 20,
      MONITORED_ACCOUNTS: 3,
      MENTIONS_PER_MONTH: 1000,
      MENTIONS_PER_SYNC: 25,
      MAX_TOTAL_MENTIONS: 100,
      STORAGE_GB: 1,
      TEAM_MEMBERS: 1,
      COOKIE_API_CALLS: 50,
    },
    "reply-god": {
      AI_CALLS: 5000,
      IMAGE_GENERATIONS: 100,
      MONITORED_ACCOUNTS: 10,
      MENTIONS_PER_MONTH: 5000,
      MENTIONS_PER_SYNC: 100,
      MAX_TOTAL_MENTIONS: 500,
      STORAGE_GB: 5,
      TEAM_MEMBERS: 3,
      COOKIE_API_CALLS: 200,
    },
    team: {
      AI_CALLS: -1, // unlimited
      IMAGE_GENERATIONS: -1, // unlimited
      MONITORED_ACCOUNTS: 50,
      MENTIONS_PER_MONTH: -1, // unlimited
      MENTIONS_PER_SYNC: 200,
      MAX_TOTAL_MENTIONS: 2000,
      STORAGE_GB: 20,
      TEAM_MEMBERS: 10,
      COOKIE_API_CALLS: -1, // unlimited
    },
  };

  const planFeatures = planLimits[planName];
  if (!planFeatures) {
    console.log(
      `⚠️ UserService: Unknown Clerk plan: ${planName}, defaulting to free limits`
    );
    return { limit: planLimits["free"][feature] || 0 };
  }

  return { limit: planFeatures[feature] || 0 };
}
