"use client";
import type React from "react";
import type { IconType } from "react-icons";
import { MdArrowOutward } from "react-icons/md";
import IconButton from "./icon-button";

interface ButtonProps {
  children: React.ReactNode;
  icon?: IconType;
  onClick: () => void;
  className?: string;
  variant?: "primary" | "secondary" | "tertiary";
  iconVariant?: "primary" | "secondary" | "tertiary";
  disabled?: boolean;
}

const PrimaryButton = ({
  icon,
  children,
  onClick,
  className,
  variant = "secondary",
  iconVariant = "primary",
  disabled = false,
}: ButtonProps) => {
  const variantClasses = {
    primary: "bg-app-secondary text-app-headline",
    secondary: "bg-app-main text-app-secondary",
    tertiary: "bg-app-stroke text-app-secondary",
  };


  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`primary-button group min-w-[240px] sm:min-w-[280px] w-auto md:w-[370px] min-h-[48px] h-[48px] sm:h-[52px] md:h-[82px] rounded-[100px] flex items-center justify-between pr-2 pl-4 sm:pl-6 md:pl-10 ${className} ${variantClasses[variant]} text-[13px] sm:text-[14px] md:text-[24px] font-sans transition-all duration-200 ${disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer hover:scale-[1.02] active:scale-[0.98]"}`}
    >
      <span className="flex-grow text-left leading-tight">{children}</span>
      {icon && (
        <span className="transition-all duration-300 flex-shrink-0 ml-2 sm:ml-4">
          <IconButton
            icon={MdArrowOutward}
            className="w-[36px] h-[36px] sm:w-[44px] sm:h-[44px] md:w-[70px] md:h-[70px] overflow-hidden"
            variant={iconVariant}
            asChild={true}
          />
        </span>
      )}
    </button>
  );
};

export default PrimaryButton;
