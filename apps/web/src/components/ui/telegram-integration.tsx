"use client";

import { useState } from "react";
import { toast } from "sonner";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON>, 
  ExternalLink, 
  Loader2, 
  MessageCircle, 
  Unlink,
  CheckCircle,
  AlertCircle,
  Clock
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { trpc } from "@/utils/trpc";

export default function TelegramIntegration() {
  const [linkCode, setLinkCode] = useState("");
  const [isLinking, setIsLinking] = useState(false);
  const [isUnlinking, setIsUnlinking] = useState(false);

  // Get linked account info
  const { 
    data: telegramData, 
    isLoading, 
    refetch 
  } = trpc.telegram.getLinkedAccount.useQuery();

  // Link account mutation
  const linkAccount = trpc.telegram.linkAccount.useMutation();

  // Unlink account mutation
  const unlinkAccount = trpc.telegram.unlinkAccount.useMutation();

  const handleLinkAccount = async () => {
    if (!linkCode.trim()) {
      toast.error("Please enter a link code from Telegram");
      return;
    }

    setIsLinking(true);
    try {
      await linkAccount.mutateAsync({ linkCode: linkCode.trim() });
      toast.success("Telegram account linked successfully!");
      setLinkCode("");
      await refetch();
    } catch (error: any) {
      console.error("Error linking account:", error);
      toast.error(error.message || "Failed to link Telegram account");
    } finally {
      setIsLinking(false);
    }
  };

  const handleUnlinkAccount = async () => {
    setIsUnlinking(true);
    try {
      await unlinkAccount.mutateAsync({ confirm: true });
      toast.success("Telegram account unlinked successfully!");
      await refetch();
    } catch (error: any) {
      console.error("Error unlinking account:", error);
      toast.error(error.message || "Failed to unlink Telegram account");
    } finally {
      setIsUnlinking(false);
    }
  };

  const copyLinkCode = () => {
    if (linkCode) {
      navigator.clipboard.writeText(linkCode);
      toast.success("Link code copied to clipboard!");
    }
  };

  const openTelegramBot = () => {
    window.open("https://t.me/Benji_BuddyChip_Bot", "_blank");
  };

  if (isLoading) {
    return (
      <Card className="bg-app-card border-app-stroke shadow-sm">
        <CardHeader>
          <CardTitle className="text-app-headline flex items-center gap-2">
            <Bot className="w-5 h-5 text-app-main" />
            Telegram Integration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin text-app-main" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-app-card border-app-stroke shadow-sm">
      <CardHeader>
        <CardTitle className="text-app-headline flex items-center gap-2">
          <Bot className="w-5 h-5 text-app-main" />
          Telegram Integration
        </CardTitle>
        <CardDescription className="text-app-headline/70">
          Connect your Telegram account to use BuddyChip AI directly in Telegram
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {telegramData?.linked ? (
          // Account is linked
          <div className="space-y-4">
            <div className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
              <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
              <div className="flex-1">
                <p className="text-sm font-medium text-green-800 dark:text-green-200">
                  Telegram account connected
                </p>
                <p className="text-xs text-green-600 dark:text-green-400">
                  @{telegramData.telegramUser?.username || telegramData.telegramUser?.firstName || "Unknown"}
                </p>
              </div>
              <Badge variant="secondary" className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                Active
              </Badge>
            </div>

            <div className="flex flex-col sm:flex-row gap-2">
              <Button
                onClick={openTelegramBot}
                className="flex-1 bg-app-main hover:bg-app-highlight text-app-secondary"
              >
                <MessageCircle className="w-4 h-4 mr-2" />
                Open Telegram Bot
                <ExternalLink className="w-4 h-4 ml-2" />
              </Button>
              <Button
                variant="outline"
                onClick={handleUnlinkAccount}
                disabled={isUnlinking}
                className="border-red-200 text-red-600 hover:bg-red-50 dark:border-red-800 dark:text-red-400 dark:hover:bg-red-900/20"
              >
                {isUnlinking ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Unlink className="w-4 h-4 mr-2" />
                )}
                Unlink
              </Button>
            </div>

            <div className="text-xs text-app-headline/60 space-y-1">
              <p>• Send messages directly to the bot for AI responses</p>
              <p>• Share Twitter URLs for instant analysis</p>
              <p>• Use /help command to see all available features</p>
            </div>
          </div>
        ) : (
          // Account is not linked
          <div className="space-y-4">
            <div className="flex items-center gap-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
              <AlertCircle className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
              <div className="flex-1">
                <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                  Telegram account not connected
                </p>
                <p className="text-xs text-yellow-600 dark:text-yellow-400">
                  Link your account to use AI features in Telegram
                </p>
              </div>
            </div>

            <div className="space-y-3">
              <div className="text-sm text-app-headline/80">
                <p className="font-medium mb-2">Steps to connect:</p>
                <ol className="list-decimal list-inside space-y-1 text-xs">
                  <li>Click "Open Telegram Bot" to start a chat</li>
                  <li>Send /start command to the bot</li>
                  <li>Click "🔗 Link Account" button in Telegram</li>
                  <li>Copy the code from Telegram and paste it below</li>
                  <li>Click "Link Account" to complete the connection</li>
                </ol>
              </div>

              <Button
                onClick={openTelegramBot}
                className="w-full bg-app-main hover:bg-app-highlight text-app-secondary"
              >
                <Bot className="w-4 h-4 mr-2" />
                Open Telegram Bot
                <ExternalLink className="w-4 h-4 ml-2" />
              </Button>

              <div className="space-y-2">
                <div className="flex gap-2">
                  <Input
                    placeholder="Enter link code from Telegram"
                    value={linkCode}
                    onChange={(e) => setLinkCode(e.target.value)}
                    className="flex-1"
                  />
                  {linkCode && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={copyLinkCode}
                      className="px-3"
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                  )}
                </div>

                <Button
                  onClick={handleLinkAccount}
                  disabled={!linkCode.trim() || isLinking}
                  className="w-full"
                >
                  {isLinking ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <CheckCircle className="w-4 h-4 mr-2" />
                  )}
                  Link Account
                </Button>
              </div>

              <div className="flex items-center gap-2 text-xs text-app-headline/60">
                <Clock className="w-4 h-4" />
                Link codes expire after 10 minutes
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
